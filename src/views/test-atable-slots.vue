<template>
  <div style="padding: 20px;">
    <h1>atable 组件插槽测试页面</h1>
    
    <div style="margin-bottom: 20px; padding: 15px; background: #f0f9ff; border-radius: 8px; border: 1px solid #bae7ff;">
      <h2>🔧 插槽配置测试</h2>
      <p>此页面用于测试 atable 组件的各种插槽功能，特别是 <code>openClose</code> 开关插槽。</p>
      
      <div style="margin-top: 15px;">
        <h3>可用插槽列表：</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-top: 10px;">
          <span v-for="slot in availableSlots" :key="slot" 
                style="background: #e6f7ff; padding: 4px 8px; border-radius: 4px; font-size: 12px; color: #1890ff;">
            {{ slot }}
          </span>
        </div>
      </div>
    </div>

    <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <h2>表格测试</h2>
      
      <atable
        :tabSourceData="testData"
        :tabColumns="testColumns"
        :apiDel="'/api/test/del'"
        :apiDelMethod="'post'"
        :buttonEditUrl="'/test/edit'"
        :total="testData.length"
        @list="refreshData"
        @pageChange="handlePageChange"
      />
    </div>

    <div style="margin-top: 30px; padding: 20px; background: #fff7e6; border-radius: 8px; border: 1px solid #ffd591;">
      <h2>📋 测试说明</h2>
      
      <h3>1. 开关插槽测试 (openClose)</h3>
      <ul>
        <li>✅ 状态字段存在时显示开关控件</li>
        <li>✅ 状态字段不存在时显示"未设置"</li>
        <li>✅ 开关状态正确映射（1=开启，2=关闭）</li>
        <li>✅ 点击开关时发送正确的API请求</li>
        <li>✅ 本地数据状态同步更新</li>
      </ul>

      <h3>2. 其他插槽测试</h3>
      <ul>
        <li>✅ <strong>imgAlert</strong>: 图片查看功能</li>
        <li>✅ <strong>operation</strong>: 编辑删除操作</li>
        <li>✅ <strong>create_time_label</strong>: 创建时间显示</li>
        <li>✅ <strong>tag</strong>: 标签显示</li>
      </ul>

      <h3>3. 插槽配置验证</h3>
      <ul>
        <li>✅ 自动检测不存在的插槽配置</li>
        <li>✅ 控制台输出详细的调试信息</li>
        <li>✅ 插槽名称映射正确处理</li>
        <li>✅ 向后兼容性保证</li>
      </ul>
    </div>

    <div style="margin-top: 20px; padding: 20px; background: #f6ffed; border-radius: 8px; border: 1px solid #b7eb8f;">
      <h2>🐛 调试信息</h2>
      <p>打开浏览器控制台查看详细的插槽配置和状态变化信息：</p>
      <ul>
        <li>组件挂载时的插槽配置检查</li>
        <li>开关状态变化的详细日志</li>
        <li>API请求和响应信息</li>
        <li>数据更新过程追踪</li>
      </ul>
    </div>

    <div style="margin-top: 20px;">
      <h2>操作按钮</h2>
      <a-button @click="addTestData" type="primary" style="margin-right: 10px;">
        添加测试数据
      </a-button>
      <a-button @click="clearTestData" style="margin-right: 10px;">
        清空数据
      </a-button>
      <a-button @click="refreshData" type="dashed">
        刷新数据
      </a-button>
    </div>
  </div>
</template>

<script>
import atable from '@/components/atable.vue'

export default {
  name: 'TestAtableSlots',
  components: {
    atable
  },
  data() {
    return {
      // 可用插槽列表（与atable组件保持一致）
      availableSlots: [
        'create_time_label',    // 创建时间
        'operation',            // 编辑删除操作
        'operationDel',         // 仅删除操作
        'imgAlert',             // 图片查看
        'configTypeLabel',      // 配置类型标签
        'configInfoAlert',      // 配置信息查看
        'toolTip',              // 文字提示
        'dataSort',             // 排序输入
        'htmlInfoAlert',        // HTML内容查看
        'tag',                  // 标签显示
        'openClose',            // 开关控制
        'infoImgUpload',        // 批量图片上传
        'infoImgUploadType2'    // 产品优势详情图片上传
      ],
      
      // 测试数据
      testData: [
        {
          id: 1,
          name: '测试产品1',
          status: 1,  // 开启状态
          image: '/uploads/test1.jpg',
          create_time: '2025-01-27 10:00:00',
          detail_cover_position: 1
        },
        {
          id: 2,
          name: '测试产品2',
          status: 2,  // 关闭状态
          image: '/uploads/test2.jpg',
          create_time: '2025-01-27 11:00:00',
          detail_cover_position: 2
        },
        {
          id: 3,
          name: '测试产品3',
          // status: undefined,  // 未设置状态
          image: '/uploads/test3.jpg',
          create_time: '2025-01-27 12:00:00'
        }
      ],
      
      // 测试表格列配置
      testColumns: [
        {
          title: 'ID',
          dataIndex: 'id',
          key: 'id',
          width: 80
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name'
        },
        {
          title: '图片',
          dataIndex: 'image',
          key: 'image',
          scopedSlots: { customRender: 'imgAlert' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          scopedSlots: { customRender: 'openClose' }
        },
        {
          title: '标签',
          dataIndex: 'detail_cover_position',
          key: 'tag',
          scopedSlots: { customRender: 'tag' }
        },
        {
          title: '创建时间',
          dataIndex: 'create_time',
          key: 'create_time',
          scopedSlots: { customRender: 'create_time_label' }
        },
        {
          title: '操作',
          key: 'operation',
          scopedSlots: { customRender: 'operation' },
          width: 200
        }
      ]
    }
  },
  
  methods: {
    refreshData() {
      console.log('刷新数据');
      this.$message.info('数据已刷新');
    },
    
    handlePageChange(pagination) {
      console.log('分页变化:', pagination);
      this.$message.info(`切换到第 ${pagination.current} 页`);
    },
    
    addTestData() {
      const newId = this.testData.length + 1;
      const newItem = {
        id: newId,
        name: `测试产品${newId}`,
        status: Math.random() > 0.5 ? 1 : 2,
        image: `/uploads/test${newId}.jpg`,
        create_time: new Date().toLocaleString(),
        detail_cover_position: Math.floor(Math.random() * 2) + 1
      };
      
      this.testData.push(newItem);
      this.$message.success('测试数据已添加');
    },
    
    clearTestData() {
      this.testData = [];
      this.$message.warning('测试数据已清空');
    }
  },
  
  mounted() {
    console.log('测试页面已加载');
    console.log('测试数据:', this.testData);
    console.log('测试列配置:', this.testColumns);
    
    // 模拟设置表格名称（atable组件需要）
    this.$utils.store.set('table', 'test_table');
  }
}
</script>

<style scoped>
h1 {
  color: #1890ff;
  border-bottom: 3px solid #f0f0f0;
  padding-bottom: 15px;
}

h2 {
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
  margin-top: 30px;
}

h3 {
  color: #666;
  margin-bottom: 15px;
}

ul, ol {
  margin: 10px 0;
  padding-left: 25px;
}

li {
  margin-bottom: 5px;
  color: #666;
}

code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  color: #d63384;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}
</style>
